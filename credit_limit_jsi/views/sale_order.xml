<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_credit_limit_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.credit.limit.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <div class="oe_title" position="before">
                    <field name="is_limit_exceed" invisible="1" />
                    <div class="alert alert-danger" role="alert" invisible="not is_limit_exceed">
                        <strong>Customer Out of credit limit</strong>
                    </div>
                </div>
                <field name="partner_shipping_id" position="after">
                    <field name="credit_limit" readonly="1" />
                    <field name="remaining_credit" readonly="1" />
                </field>
                <group name="sale_total" position="before">
                    <group class="oe_subtotal_footer" colspan="2">
                        <field name="amount_discounted" readonly="1" colspan="2" />
                    </group>
                </group>
            </field>
        </record>
    </data>
</odoo>
