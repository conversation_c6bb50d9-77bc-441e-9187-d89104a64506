<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <!-- Make Move lines readonly if user belong to group_block_add_a_line group -->
        <record id="view_picking_form_readonly_partner_misc_jsi" model="ir.ui.view">
            <field name="name">stock.picking.form.readonly.partner.misc.jsi</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form" />
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <field name="is_block_adding_line" invisible="1" />
                </xpath>
                <xpath expr="//field[@name='move_ids_without_package']" position="attributes">
                    <attribute name="readonly" separator=" or " add="(is_block_adding_line)" />
                </xpath>
            </field>
        </record>
    </data>
</odoo>
