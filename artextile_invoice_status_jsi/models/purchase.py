from odoo import api, fields, models


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    invoice_status_ar = fields.Selection(
        [
            ("no", "Nothing to Bill"),
            ("to invoice", "Waiting Bills"),
            ("invoiced", "Fully Billed"),
            ("to_credit_note", "To Credit Note"),
            ("credit_note_created", "Credit Note Created"),
            ("credit_note_posted", "Credit Note Posted"),
        ],
        string="Artextile Billing Status",
        compute="_compute_invoice_status_ar",
    )

    @api.depends()
    def _compute_invoice_status_ar(self):
        """
        to_credit_note:
            - return receipt validated, but no credit note created yet
            - return receipt fully or patially, but no credit note created yet
        credit_note_created:
            - return receipt validated, credit note is in draft state
        credit_note_posted:
            - return receipt validated, credit note is in posted state
        """

        for order in self:
            if order.state not in ("purchase", "done"):
                order.invoice_status_ar = "no"
                continue
            invoice_status_ar = False
            return_receipt = order.picking_ids.filtered(
                lambda x: x.picking_type_code == "outgoing" and x.state != "cancel"
            )
            if return_receipt:
                refund_invoice_draft = order.invoice_ids.filtered(
                    lambda x: x.move_type == "in_refund" and x.state == "draft"
                )
                refund_invoice_posted = order.invoice_ids.filtered(
                    lambda x: x.move_type == "in_refund" and x.state == "posted"
                )
                if refund_invoice_draft:
                    invoice_status_ar = "credit_note_created"
                elif refund_invoice_posted:
                    invoice_status_ar = "credit_note_posted"
                elif any(
                    line.qty_received < line.product_qty and line.qty_invoiced == line.product_qty
                    for line in order.order_line.filtered(lambda l: not l.display_type)
                ):
                    invoice_status_ar = "to_credit_note"
            if invoice_status_ar:
                order.invoice_status_ar = invoice_status_ar
                continue
            if any(
                line.qty_received != 0 and (line.qty_invoiced == 0 or line.qty_invoiced < line.qty_received)
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "to invoice"
            elif any(
                line.qty_received == 0 and line.qty_invoiced == 0
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "no"
            elif all(
                line.product_qty == line.qty_received and line.product_qty == line.qty_invoiced
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "invoiced"
            else:
                order.invoice_status_ar = "no"
