from dateutil.relativedelta import relativedelta

from odoo import _, api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    full_name = fields.Char("Company Name", compute="_compute_contact_details", readonly=False, store=True)
    contact = fields.Char("Contact", compute="_compute_contact_details", readonly=False, store=True)
    sale_uom_id = fields.Many2one("uom.uom", string="Sale UOM")

    @api.depends("partner_id")
    def _compute_contact_details(self):
        for sale in self:
            if sale.partner_id:
                sale.full_name = sale.partner_id.full_name
                sale.contact = sale.partner_id.contact

    def _prepare_invoice(self):
        """ Override to pass payment reference to invoice """
        invoice_vals = super()._prepare_invoice()
        invoice_vals.update({"payment_reference": self.name, "invoice_date": self.date_order})
        return invoice_vals

    def button_mass_update_uom(self):
        """Mass update UOM on sale.order.line"""
        self.ensure_one()
        if self.sale_uom_id and self.order_line:
            lines_with_same_uom_categ = self.order_line.filtered(
                lambda x: x.product_uom.category_id == self.sale_uom_id.category_id
            )
            lines_with_same_uom_categ.write({"product_uom": self.sale_uom_id.id})


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    @api.onchange("secondary_uom_qty")
    def line_data_onchange(self):
        """
        Check if added line is existed withing given days range
        Only for Version product, with same master_product_id and same partner_id
        TODO: Going with onchange for non-blocking warning, other options:
                1) On create/write, will be blocking warning
                    - can avoid by checkbox, but no
                2) Button is also an option, but we need to click eachtime
        """
        if not self.env.company.use_so_days_limit:
            return

        if self.product_id and self.product_id.master_product_id and self.order_partner_id:
            so_days_limit = fields.Datetime.today() - relativedelta(days=self.env.company.so_days_limit)
            order_lines = self.search(
                [
                    ("order_id.date_order", ">", so_days_limit),
                    ("order_id.state", "in", ["sale", "done"]),
                    ("product_id.master_product_id", "=", self.product_id.master_product_id.id),
                    ("secondary_uom_qty", "=", self.secondary_uom_qty),
                    ("order_partner_id", "=", self.order_partner_id.id),
                ]
            )
            if order_lines:
                line_details = "\n".join(
                    [
                        "%s, %s, %s"
                        % (l.order_id.date_order.strftime("%d-%m-%Y %H:%M:%S"), l.order_id.name, l.customer_po or "N/A")
                        for l in order_lines
                    ]
                )
                return {
                    "warning": {
                        "title": _("Duplicated warning..."),
                        "message": _("Duplicated order line exists in below order(s):\n %s" % line_details),
                    }
                }

    @api.onchange("product_id")
    def _onchange_product_id_warning(self):
        """OVERRIDE
            Check if Product's Free Inv. is less than SO qty limit
                - only for storable product
                - if product is with bom, check it's component's Free Inv.
        """
        res = super()._onchange_product_id_warning()
        if not res and self.env.company.use_so_qty_limit and self.product_id.type == "product":
            if not self.product_id.bom_ids:
                products = [self.product_id]
            else:
                bom = self.env["mrp.bom"]._bom_find(products=self.product_id, company_id=self.company_id.id)
                products = bom.get(self.product_id).bom_line_ids.product_id
            res = self.check_so_qty_limit(products, self.env.company.so_qty_limit)
        return res

    def _compute_product_uom(self):
        """
            Force change line UOM to sales global UOM if both falls in same UOM category
        """
        super()._compute_product_uom()
        for line in self:
            if line.order_id.sale_uom_id.category_id == line.product_id.uom_id.category_id:
                line.product_uom = line.order_id.sale_uom_id

    def check_so_qty_limit(self, products, so_qty_limit):
        for product in products:
            if product.qty_total < so_qty_limit:
                return {
                    "warning": {
                        "title": _("Inventory warning..."),
                        "message": _(
                            "The inventory for %s will be under Minimum Stock level (%f Meter)\n"
                            "Current Free Inv.: %f Meter" % (product.display_name, so_qty_limit, product.qty_total)
                        ),
                    }
                }
