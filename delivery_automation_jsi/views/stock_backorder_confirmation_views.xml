<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_backorder_confirmation_delivery_automation_jsi" model="ir.ui.view">
        <field name="name">stock.backorder.confirmation.delivery.automation.jsi</field>
        <field name="model">stock.backorder.confirmation</field>
        <field name="inherit_id" ref="stock.view_backorder_confirmation" />
        <field name="arch" type="xml">
            <button name="process" position="attributes">
                <attribute name="context">{'block_invoice_creation': 1}</attribute>
            </button>
            <button name="process_cancel_backorder" position="attributes">
                <attribute name="context">{'block_invoice_creation': 1}</attribute>
            </button>
        </field>
    </record>
</odoo>
