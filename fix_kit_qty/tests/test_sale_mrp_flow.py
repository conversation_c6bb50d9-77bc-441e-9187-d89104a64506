from odoo.exceptions import UserError
from odoo.tests import Form

from odoo.addons.sale_mrp.tests.test_sale_mrp_flow import TestSaleMrpFlow


def test_sale_decimal_kit_quantity(self):
    """When selling a decimal quantity of a kit, this test ensure that the delivered quantity
    on the sale order is correct"""
    stock_location = self.company_data["default_warehouse"].lot_stock_id
    self.env["stock.quant"]._update_available_quantity(self.component_a, stock_location, 20)
    self.env["stock.quant"]._update_available_quantity(self.component_b, stock_location, 10)
    self.env["stock.quant"]._update_available_quantity(self.component_c, stock_location, 30)

    f = Form(self.env["sale.order"])
    f.partner_id = self.partner_a
    with f.order_line.new() as line:
        line.product_id = self.kit_1
        line.product_uom_qty = 8.25
    so = f.save()
    so.action_confirm()

    picking = so.picking_ids[0]
    move_ids = picking.move_ids
    expected_quantities = {
        self.component_a: 16.50,
        self.component_b: 8.25,
        self.component_c: 24.75,
    }
    self._assert_quantities(move_ids, expected_quantities)

    wiz_act = picking.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save().process()

    self.assertEqual(so.order_line.qty_delivered, 8.25)


def new_test_01_sale_mrp_delivery_kit(self):
    """ Test delivered quantity on SO based on delivered quantity in pickings."""
    # intial so
    product = self.env["product.product"].create(
        {
            "name": "Table Kit",
            "type": "consu",
            "invoice_policy": "delivery",
            "categ_id": self.env.ref("product.product_category_all").id,
        }
    )
    # Remove the MTO route as purchase is not installed and since the procurement removal the exception is directly raised
    product.write({"route_ids": [(6, 0, [self.company_data["default_warehouse"].manufacture_pull_id.route_id.id])]})

    product_wood_panel = self.env["product.product"].create(
        {
            "name": "Wood Panel",
            "type": "product",
        }
    )
    product_desk_bolt = self.env["product.product"].create(
        {
            "name": "Bolt",
            "type": "product",
        }
    )
    self.env["mrp.bom"].create(
        {
            "product_tmpl_id": product.product_tmpl_id.id,
            "product_uom_id": self.env.ref("uom.product_uom_unit").id,
            "sequence": 2,
            "type": "phantom",
            "bom_line_ids": [
                (
                    0,
                    0,
                    {
                        "product_id": product_wood_panel.id,
                        "product_qty": 1,
                        "product_uom_id": self.env.ref("uom.product_uom_unit").id,
                    },
                ),
                (
                    0,
                    0,
                    {
                        "product_id": product_desk_bolt.id,
                        "product_qty": 4,
                        "product_uom_id": self.env.ref("uom.product_uom_unit").id,
                    },
                ),
            ],
        }
    )

    partner = self.env["res.partner"].create({"name": "My Test Partner"})
    # if `delivery` module is installed, a default property is set for the carrier to use
    # However this will lead to an extra line on the SO (the delivery line), which will force
    # the SO to have a different flow (and `invoice_state` value)
    if "property_delivery_carrier_id" in partner:
        partner.property_delivery_carrier_id = False

    f = Form(self.env["sale.order"])
    f.partner_id = partner
    with f.order_line.new() as line:
        line.product_id = product
        line.product_uom_qty = 5
    so = f.save()

    # confirm our standard so, check the picking
    so.action_confirm()
    self.assertTrue(so.picking_ids, 'Sale MRP: no picking created for "invoice on delivery" storable products')

    # invoice in on delivery, nothing should be invoiced
    with self.assertRaises(UserError):
        so._create_invoices()
    self.assertEqual(
        so.invoice_status, "no", 'Sale MRP: so invoice_status should be "nothing to invoice" after invoicing'
    )

    # deliver partially (1 of each instead of 5), check the so's invoice_status and delivered quantities
    pick = so.picking_ids
    pick.move_ids.write({"quantity_done": 1})
    wiz_act = pick.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save()
    wiz.process()
    self.assertEqual(
        so.invoice_status,
        "to invoice",
        'Sale MRP: so invoice_status should be "to invoice" since 0.25 kit is delivered',
    )
    del_qty = sum(sol.qty_delivered for sol in so.order_line)
    self.assertEqual(del_qty, 0.25)
    # deliver remaining products, check the so's invoice_status and delivered quantities
    self.assertEqual(len(so.picking_ids), 2, "Sale MRP: number of pickings should be 2")
    pick_2 = so.picking_ids.filtered("backorder_id")
    for move in pick_2.move_ids:
        if move.product_id.id == product_desk_bolt.id:
            move.write({"quantity_done": 19})
        else:
            move.write({"quantity_done": 4})
    pick_2.button_validate()

    del_qty = sum(sol.qty_delivered for sol in so.order_line)
    self.assertEqual(del_qty, 5.0, "Sale MRP: delivered quantity should be 5.0 after complete delivery of a kit")
    self.assertEqual(
        so.invoice_status,
        "to invoice",
        'Sale MRP: so invoice_status should be "to invoice" after complete delivery of a kit',
    )


def new_test_04_sale_mrp_kit_qty_delivered(self):
    """Test that the quantities delivered are correct when
    a kit with subkits is ordered with multiple backorders and returns
    """

    # 'kit_parent' structure:
    # ---------------------------
    #
    # kit_parent --|- kit_2 x2 --|- component_d x1
    #              |             |- kit_1 x2 -------|- component_a   x2
    #              |                                |- component_b   x1
    #              |                                |- component_c   x3
    #              |
    #              |- kit_3 x1 --|- component_f x1
    #              |             |- component_g x2
    #              |
    #              |- component_e x1

    # Updating the quantities in stock to prevent
    # a 'Not enough inventory' warning message.
    stock_location = self.company_data["default_warehouse"].lot_stock_id
    self.env["stock.quant"]._update_available_quantity(self.component_a, stock_location, 56)
    self.env["stock.quant"]._update_available_quantity(self.component_b, stock_location, 28)
    self.env["stock.quant"]._update_available_quantity(self.component_c, stock_location, 84)
    self.env["stock.quant"]._update_available_quantity(self.component_d, stock_location, 14)
    self.env["stock.quant"]._update_available_quantity(self.component_e, stock_location, 7)
    self.env["stock.quant"]._update_available_quantity(self.component_f, stock_location, 14)
    self.env["stock.quant"]._update_available_quantity(self.component_g, stock_location, 28)

    # Creation of a sale order for x7 kit_parent
    partner = self.env["res.partner"].create({"name": "My Test Partner"})
    f = Form(self.env["sale.order"])
    f.partner_id = partner
    with f.order_line.new() as line:
        line.product_id = self.kit_parent
        line.product_uom_qty = 7.0

    so = f.save()
    so.action_confirm()

    # Check picking creation, its move lines should concern
    # only components. Also checks that the quantities are corresponding
    # to the SO
    self.assertEqual(len(so.picking_ids), 1)
    order_line = so.order_line[0]
    picking_original = so.picking_ids[0]
    move_ids = picking_original.move_ids
    products = move_ids.mapped("product_id")
    kits = [self.kit_parent, self.kit_3, self.kit_2, self.kit_1]
    components = [
        self.component_a,
        self.component_b,
        self.component_c,
        self.component_d,
        self.component_e,
        self.component_f,
        self.component_g,
    ]
    expected_quantities = {
        self.component_a: 56.0,
        self.component_b: 28.0,
        self.component_c: 84.0,
        self.component_d: 14.0,
        self.component_e: 7.0,
        self.component_f: 14.0,
        self.component_g: 28.0,
    }

    self.assertEqual(len(move_ids), 7)
    self.assertTrue(not any(kit in products for kit in kits))
    self.assertTrue(all(component in products for component in components))
    self._assert_quantities(move_ids, expected_quantities)

    # Process only 7 units of each component
    qty_to_process = 7
    move_ids.write({"quantity_done": qty_to_process})

    # Create a backorder for the missing componenents
    wiz_act = picking_original.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save().process()

    # Check that a backorded is created
    self.assertEqual(len(so.picking_ids), 2)
    backorder_1 = so.picking_ids - picking_original
    self.assertEqual(backorder_1.backorder_id.id, picking_original.id)

    # Check that a partial qty of the kit is delivered
    self.assertEqual(order_line.qty_delivered, 0.58)

    # Process just enough components to make 1 kit_parent
    qty_to_process = {
        self.component_a: 1,
        self.component_c: 5,
    }
    self._process_quantities(backorder_1.move_ids, qty_to_process)

    # Create a backorder for the missing componenents
    wiz_act = backorder_1.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save().process()

    # Only 1 kit_parent should be delivered at this point
    self.assertEqual(order_line.qty_delivered, 1)

    # Check that the second backorder is created
    self.assertEqual(len(so.picking_ids), 3)
    backorder_2 = so.picking_ids - picking_original - backorder_1
    self.assertEqual(backorder_2.backorder_id.id, backorder_1.id)

    # Set the components quantities that backorder_2 should have
    expected_quantities = {
        self.component_a: 48,
        self.component_b: 21,
        self.component_c: 72,
        self.component_d: 7,
        self.component_f: 7,
        self.component_g: 21,
    }

    # Check that the computed quantities are matching the theorical ones.
    # Since component_e was totally processed, this componenent shouldn't be
    # present in backorder_2
    self.assertEqual(len(backorder_2.move_ids), 6)
    move_comp_e = backorder_2.move_ids.filtered(lambda m: m.product_id.id == self.component_e.id)
    self.assertFalse(move_comp_e)
    self._assert_quantities(backorder_2.move_ids, expected_quantities)

    # Process enough components to make x3 kit_parents
    qty_to_process = {self.component_a: 16, self.component_b: 5, self.component_c: 24, self.component_g: 5}
    self._process_quantities(backorder_2.move_ids, qty_to_process)

    # Create a backorder for the missing componenents
    wiz_act = backorder_2.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save().process()

    # Check that x3 kit_parents are indeed delivered
    self.assertEqual(order_line.qty_delivered, 3)

    # Check that the third backorder is created
    self.assertEqual(len(so.picking_ids), 4)
    backorder_3 = so.picking_ids - (picking_original + backorder_1 + backorder_2)
    self.assertEqual(backorder_3.backorder_id.id, backorder_2.id)

    # Check the components quantities that backorder_3 should have
    expected_quantities = {
        self.component_a: 32,
        self.component_b: 16,
        self.component_c: 48,
        self.component_d: 7,
        self.component_f: 7,
        self.component_g: 16,
    }
    self._assert_quantities(backorder_3.move_ids, expected_quantities)

    # Process all missing components
    self._process_quantities(backorder_3.move_ids, expected_quantities)

    # Validating the last backorder now it's complete.
    # All kits should be delivered
    backorder_3.button_validate()
    self.assertEqual(order_line.qty_delivered, 7.0)

    # Return all components processed by backorder_3
    stock_return_picking_form = Form(
        self.env["stock.return.picking"].with_context(
            active_ids=backorder_3.ids, active_id=backorder_3.ids[0], active_model="stock.picking"
        )
    )
    return_wiz = stock_return_picking_form.save()
    for return_move in return_wiz.product_return_moves:
        return_move.write({"quantity": expected_quantities[return_move.product_id], "to_refund": True})
    res = return_wiz.create_returns()
    return_pick = self.env["stock.picking"].browse(res["res_id"])

    # Process all components and validate the picking
    wiz_act = return_pick.button_validate()
    wiz = Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save()
    wiz.process()

    # Now quantity delivered should be 3 again
    self.assertEqual(order_line.qty_delivered, 3)

    stock_return_picking_form = Form(
        self.env["stock.return.picking"].with_context(
            active_ids=return_pick.ids, active_id=return_pick.ids[0], active_model="stock.picking"
        )
    )
    return_wiz = stock_return_picking_form.save()
    for move in return_wiz.product_return_moves:
        move.quantity = expected_quantities[move.product_id]
    res = return_wiz.create_returns()
    return_of_return_pick = self.env["stock.picking"].browse(res["res_id"])

    # Process all components except one of each
    for move in return_of_return_pick.move_ids:
        move.write({"quantity_done": expected_quantities[move.product_id] - 1, "to_refund": True})

    wiz_act = return_of_return_pick.button_validate()
    Form(self.env[wiz_act["res_model"]].with_context(wiz_act["context"])).save().process()

    self.assertEqual(order_line.qty_delivered, 6.5)

    # Check that the 4th backorder is created.
    self.assertEqual(len(so.picking_ids), 7)
    backorder_4 = so.picking_ids - (
        picking_original + backorder_1 + backorder_2 + backorder_3 + return_of_return_pick + return_pick
    )
    self.assertEqual(backorder_4.backorder_id.id, return_of_return_pick.id)

    # Check the components quantities that backorder_4 should have
    for move in backorder_4.move_ids:
        self.assertEqual(move.product_qty, 1)


TestSaleMrpFlow.test_01_sale_mrp_delivery_kit = new_test_01_sale_mrp_delivery_kit
TestSaleMrpFlow.test_04_sale_mrp_kit_qty_delivered = new_test_04_sale_mrp_kit_qty_delivered
TestSaleMrpFlow.test_04_sale_mrp_kit_qty_delivered = test_sale_decimal_kit_quantity
