/** @odoo-module **/

import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import { useState } from "@odoo/owl";
import { CheckBox } from "@web/core/checkbox/checkbox";
import { cookie } from "@web/core/browser/cookie";
import { _t } from "@web/core/l10n/translation";

export const IGNORE_REMINDER_TODAY_COOKIE = "ignore_reminder_today";

export class DeliveryLeadTimeReminderDialog extends ConfirmationDialog {
    static props = {
        ...ConfirmationDialog.props,
    };
    static template = "website_artextile_jsi.DeliveryLeadTimeReminderDialog";
    static components = {
        ...ConfirmationDialog.components,
        CheckBox,
    };
    static defaultProps = {
        ...ConfirmationDialog.defaultProps,
        confirmLabel: _t("Confirm"),
    };

    setup() {
        super.setup();
        this.state = useState({ ignoreRemindToday: true });
    }

    _confirm() {
        this.execButton(() => {
            if (this.state.ignoreRemindToday) {
                cookie.set(
                    IGNORE_REMINDER_TODAY_COOKIE,
                    true,
                    1 * 24 * 60 * 60
                ); // 1 day cookie.
            } else {
                cookie.delete(IGNORE_REMINDER_TODAY_COOKIE);
            }
            return this.props.confirm();
        });
    }

    onIgnoreRemindTodayChange(checked) {
        this.state.ignoreRemindToday = checked;
    }
}
