<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <!-- This will used here: https://github.com/odoo/odoo/blob/17.0/addons/website_sale_stock/static/src/js/variant_mixin.js#L84 -->
    <!-- <t t-extend="product_availability"> -->
        <!--
            - This is for PRODUCT FORM on website shop
            - Remove all the stock related info, replace with simple message
        -->
        <!-- If product availability in [always,  threshold] -->
        <!-- <t t-jquery="t[t-if='virtual_available gt 0']" t-operation="replace">
            <t t-if="virtual_available gt stock_qty_threshold">
                <div t-if="virtual_available lt available_threshold and inventory_availability == 'threshold'" t-attf-class="availability_message_#{product_template} text-warning-light-ar mt16">
                    <i class="fa fa-exclamation-triangle" title="Warning" role="img" aria-label="Warning"/>
                    <span>Low Stock</span>
                </div>
                <div t-else="" t-attf-class="availability_message_#{product_template} text-success mt16">
                    <div>&amp;nbsp;Available&amp;nbsp;</div>
                    <div class="text-body" t-out="shipping_info"/>
                </div>
            </t>
            <t t-else="">
                <div t-attf-class="availability_message_#{product_template} text-warning-ar mt16">
                    <i class="fa fa-exclamation-triangle" title="Sold Out" role="img" aria-label="Sold Out"/>
                    <span>Sold Out</span>
                </div>
            </t>
        </t> -->
        <!-- If product availability = never -->
        <!-- <t t-jquery="div:last" t-operation="after">
            <div t-if="product_type == 'product' and inventory_availability == 'never'" t-attf-class="availability_message_#{product_template} text-success mt16">
                <span>&amp;nbsp;&amp;nbsp;Available</span>
                <div class="text-body" t-out="shipping_info"/>
            </div>
        </t> -->
        <!-- Remove 'Temporarily out of stock' message -->
        <!-- <t t-jquery="div[t-if='!cart_qty and virtual_available lte 0']" t-operation="replace"/> -->
    <!-- </t> -->
</templates>
