<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <template id="external_layout_artextile_jsi">
        <t t-if="not doc" t-set="doc" t-value="o"/>
        <div class="header font-size14">
            <table width="100%" style="table-layout: fixed;" class="header-table-ar table-borderless">
                <tr>
                    <td width="30%">
                        <span t-field="doc.company_id.address3" /><br />
                        <span t-field="doc.company_id.address2" /><br />
                        <span t-field="doc.company_id.address1" /><br />
                        電話: <span t-field="doc.company_id.phone" /><br />
                        傳真: <span t-field="doc.company_id.partner_id.fax" />
                    </td>
                    <td class="text-center" width="40%" style="vertical-align: bottom;">
                        <img t-if="doc.company_id.logo" t-att-src="image_data_uri(doc.company_id.logo)" style="max-height: 70px;" />
                    </td>
                    <td class="text-end">
                        <span t-field="doc.company_id.street" /><br />
                        <span t-field="doc.company_id.street2" /><br />
                        <span t-field="doc.company_id.city" />
                        <span t-field="doc.company_id.country_id" /><br />
                        TEL: <span t-field="doc.company_id.phone" /><br />
                        FAX: <span t-field="doc.company_id.partner_id.fax" />
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <br />
                    </td>
                </tr>
                <tr t-if="doc._name == 'sale.order'">
                    <t t-call="custom_reports_artextile_jsi.sale_header_address_document_artextile_jsi" />
                </tr>
                <tr t-else="">
                    <t t-call="custom_reports_artextile_jsi.purchase_header_address_document_artextile_jsi" />
                </tr>
            </table>
        </div>
        <div class="article o_report_layout_standard" t-att-data-oe-model="doc._name" t-att-data-oe-id="doc.id" t-att-data-oe-lang="doc.env.context.get('lang')">
            <t t-out="0" />
        </div>
        <div class="footer o_standard_footer text-center font-size15">
            <div class="mb-3">
                ------------------- Distributor of Quality Imported Fabrics since 1966 -------------------
            </div>
            <p>Page: <span class="page" /> / <span class="topage" /></p>
        </div>
    </template>

    <template id="sale_header_address_document_artextile_jsi">
        <t t-set="cust_br">
            <!-- This will add blank space if t-field is empty, this way space between 2 span will remain same -->
            <span style="color: white;">&amp;nbsp;</span><br/>
        </t>
        <td class="text-nowrap overflow-hidden">
            BILL TO (客戶信息) <t t-out="cust_br"/>
            <strong t-field="doc.full_name" /><t t-out="cust_br"/>
            <span t-field="doc.partner_id.street" /><t t-out="cust_br"/>
            <span t-field="doc.partner_id.street2" /><t t-out="cust_br"/>
            <span t-field="doc.partner_id.city" />
            <span t-field="doc.partner_id.state_id" />
            <span t-field="doc.partner_id.country_id" /><t t-out="cust_br"/>
            TEL: <span t-field="doc.partner_id.phone" /><t t-out="cust_br"/>
            CODE: <span t-field="doc.partner_id.code" />
        </td>
        <td class="text-center" style="vertical-align: bottom;">
            <h5>
                <strong>
                    <span t-if="doc.state in ['draft','sent']">QUOTATION #</span>
                    <span t-if="doc.state not in ['draft','sent']">INVOICE #</span>
                    <span t-field="doc.name" />
                </strong>
                <span style="color: white;">.</span> <!-- This will move whole content bottom of td -->
            </h5>
            <span>DATE:</span>
            <span t-field="doc.date_order" t-options="{'widget': 'date', 'format': 'dd MMMM, yyyy'}" />
            <div t-if="not customer_copy" t-field="doc.date_order" t-options="{'widget': 'datetime', 'format': 'HHmm'}"/>
        </td>
        <td class="text-end text-nowrap overflow-hidden">
            DELIVERY TO (收貨地址):<t t-out="cust_br"/>
            <strong t-field="doc.partner_shipping_id.full_name" /><t t-out="cust_br"/>
            <span t-field="doc.partner_shipping_id.street" /><t t-out="cust_br"/>
            <span t-field="doc.partner_shipping_id.street2" /><t t-out="cust_br"/>
            <span t-field="doc.partner_shipping_id.city" />
            <span t-field="doc.partner_shipping_id.state_id" />
            <span t-field="doc.partner_shipping_id.country_id" /><t t-out="cust_br"/>
            TEL: <span t-field="doc.partner_shipping_id.phone" /><t t-out="cust_br"/>
            CONTACT: <span t-field="doc.partner_shipping_id.contact" />
        </td>
    </template>

    <template id="purchase_header_address_document_artextile_jsi">
        <td class="text-nowrap overflow-hidden">
            VENDOR (供應商):<br />
            <strong t-field="doc.partner_id.full_name" /><br />
            <span t-field="doc.partner_id.street" /><br />
            <span t-field="doc.partner_id.street2" /><br />
            <span t-field="doc.partner_id.city" />
            <span t-field="doc.partner_id.state_id" />
            <span t-field="doc.partner_id.country_id" /><br />
            TEL: <span t-field="doc.partner_id.phone" /><br />
            FAX: <span t-field="doc.partner_id.fax" />
        </td>
        <td class="text-center" style="vertical-align: bottom;">
            <h5><strong>PURCHASE ORDER</strong></h5>
            <span t-field="doc.name" /><br />
            <span>DATE:</span> <span t-field="doc.date_order" t-options="{'widget': 'date', 'format': 'dd MMMM, yyyy'}"/>
        </td>
        <td class="text-end text-nowrap overflow-hidden">
            DELIVERY TO (收貨地址):<br />
            <span t-field="doc.company_id" /><br />
            <span t-field="doc.company_id.street" /><br />
            <span t-field="doc.company_id.street2" /><br />
            <span t-field="doc.company_id.city" />
            <span t-field="doc.company_id.state_id" />
            <span t-field="doc.company_id.country_id" /><br />
            TEL: <span t-field="doc.company_id.phone" /><br />
            FAX: <span t-field="doc.company_id.partner_id.fax" />
        </td>
    </template>

</odoo>
