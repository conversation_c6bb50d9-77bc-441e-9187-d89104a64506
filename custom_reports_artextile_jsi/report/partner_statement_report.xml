<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="report_partner_statement_artextile_jsi">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <!-- HEADER -->
                <div class="header text-center">
                    <t t-set="header_data" t-value="env.context.get('header_data')" />
                    <h2><strong t-field="res_company.name" /></h2>
                    <div t-field="res_company.street" />
                    <div>
                        <span t-field="res_company.street2" />
                        <span t-field="res_company.city" />
                        <span t-field="res_company.state_id.name" />
                        <span t-field="res_company.country_id" />
                    </div>
                    <div>
                        Tel: <span t-field="res_company.phone" />
                        Fax: <span t-field="res_company.partner_id.fax" />
                    </div>
                    <h2><strong>Statement &amp;nbsp;&amp;nbsp;&amp;nbsp; 對賬單</strong></h2>
                    <h4>
                        Period 期間：From 自<span t-out="header_data['start_date']" />
                        To 至<span t-out="header_data['end_date']" />
                    </h4>
                    <style>.header_table td {border: 1px solid #E6E7E8; text-align: left;padding: 4px;}</style>
                    <table width="100%" class="header_table">
                        <tr>
                            <td width="55%">
                                <strong>Customer</strong> : <span t-out="header_data['full_name']" />
                            </td>
                            <td>
                                <strong>Customer Code</strong> : <span t-out="header_data['code']" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Address</strong> : <span t-out="header_data['address']" />
                            </td>
                            <td>
                                <strong>Payment Terms</strong> : <span t-out="header_data['payment_term']" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Tel</strong> : <span t-out="header_data['phone']" />
                            </td>
                            <td><strong>Currency</strong> : HKD </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Fax</strong> : <span t-out="header_data['fax']" />
                            </td>
                            <td />
                        </tr>
                    </table>
                </div>
                <!-- BODY -->
                <div class="page">
                    <style>
                        .inv_table th,
                        .inv_table td {border: 1px solid #E6E7E8; text-align: center;}
                    </style>
                    <br />
                    <br />
                    <t t-set="widget_float" t-value="{'widget': 'float', 'precision': 2}"/>
                    <h5><strong>Invoices of Current Period 本期交易：</strong></h5>
                    <table width="100%" class="inv_table">
                        <thead>
                            <tr style="border-bottom: 2px solid #C0C3C4;">
                                <th width="25%"><span>Inv. Date (d/m/y)<br />發票日期(日/月/年)</span></th>
                                <th width="25%"><span>Invoice No.<br />發票單號</span></th>
                                <th width="25%"><span>Invoice Amt.<br />發票金額 (HKD)</span></th>
                                <th><span>Remaining Amt.<br />結餘金額 (HKD)</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="total_residual_amount" t-value="0" />
                            <tr t-foreach="env.context.get('current_preriod_data')" t-as="inv">
                                <td>
                                    <t t-out="inv['invoice_date']" />
                                </td>
                                <td>
                                    <span t-out="inv['name']" />
                                </td>
                                <td>
                                    <span t-out="inv['amount_total']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="inv['amount_residual']" t-options="widget_float" />
                                    <t t-set="total_residual_amount" t-value="total_residual_amount + inv['amount_residual']" />
                                </td>
                            </tr>
                            <tr style="border-top: 2px solid #C0C3C4;">
                                <td />
                                <td />
                                <td />
                                <td>
                                    <strong t-out="total_residual_amount" t-options="widget_float" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="page-break-inside: avoid;">
                        <br />
                        <br />
                        <h5>
                            <strong>Summary 總結：</strong>
                        </h5>
                        <table width="40%" class="inv_table">
                            <thead>
                                <tr style="border-bottom: 2px solid #C0C3C4;">
                                    <th width="20%">Monthly Inv.<br />發票月份</th>
                                    <th width="20%">Amount<br />金額 (HKD)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="total_residual_amount" t-value="0" />
                                <tr t-foreach="env.context.get('due_invoice_data')" t-as="inv">
                                    <td><t t-out="inv" /></td>
                                    <td>
                                        <t
                                            t-set="total_residual_amount"
                                            t-value="total_residual_amount + env.context.get('due_invoice_data')[inv]['sum']"
                                        />
                                        <span t-out="env.context.get('due_invoice_data')[inv]['sum']" t-options="widget_float" />
                                    </td>
                                </tr>
                                <tr style="border-top: 2px solid #C0C3C4;">
                                    <td />
                                    <td>
                                        <strong t-out="total_residual_amount" t-options="widget_float" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- FOOTER -->
                <div class="footer o_standard_footer" style="font-size: 12px;">
                    <div class="text-center text-muted">
                        Page: <span class="page" /> / <span class="topage" />
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>
