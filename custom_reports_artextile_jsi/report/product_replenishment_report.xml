<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="report_replenishment_artextile_jsi">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <!-- HEADER -->
                <div class="header text-center">
                    <t t-set="header_data" t-value="env.context.get('header_data')" />
                    <h2><strong>Inventory Report</strong></h2>
                    <div>
                        Reporting Date:
                        <span t-out="time.strftime('%d/%B/%y')" /> <br />
                        Bulk Threshold:
                        <span t-out="header_data['bulk_threshold']" />
                    </div>
                    <h4>
                        Sales Period 期間 : From 自
                        <span t-out="header_data['start_date']" />
                        To 至
                        <span t-out="header_data['end_date']" />
                    </h4>
                </div>
                <!-- BODY -->
                <div class="page">
                    <style>
                        .inv_table th,
                    .inv_table td {font-size: 12px; border: 1px solid #E6E7E8; text-align: center;}
                    </style>
                    <br />
                    <br />
                    <t t-set="widget_float" t-value="{'widget': 'float', 'precision': 2}"/>
                    <table width="100%" class="inv_table">
                        <thead>
                            <tr style="border-bottom: 2px solid #C0C3C4;">
                                <th>Internal reference</th>
                                <th>Master No.</th>
                                <th>Total CL</th>
                                <th>Total Bulk</th>
                                <th>Mth CL</th>
                                <th>Mth Bulk</th>
                                <th>Sust. Mth(OH)</th>
                                <th>Sust. Mth(OH+PO)</th>
                                <th>Free inventory</th>
                                <th>PO</th>
                                <th>Quick service?</th>
                                <th>Vendor</th>
                                <th>Vendor product code</th>
                                <th>Attribute details</th>
                                <th>Product name</th>
                                <th width="12%">Version family</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td t-foreach="16" t-as="l">&amp;nbsp;</td>
                            </tr>
                            <tr t-foreach="env.context.get('report_data')" t-as="line">
                                <td>
                                    <span t-out="line['default_code']" />
                                </td>
                                <td>
                                    <span t-out="line['master_no']" />
                                </td>
                                <td>
                                    <span t-out="line['total_cl']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['total_bk']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['mth_sales_cl']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['mth_sales_bk']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['sust_mth_oh']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['sust_mth_oh_po']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['free_inventory']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['po']" t-options="widget_float" />
                                </td>
                                <td>
                                    <span t-out="line['quick_service']" />
                                </td>
                                <td>
                                    <span t-out="line['vendor']" />
                                </td>
                                <td>
                                    <span t-out="line['vendor_item_no']" />
                                </td>
                                <td>
                                    <span t-out="line['attribute_details']" />
                                </td>
                                <td>
                                    <span t-out="line['product_name']" />
                                </td>
                                <td>
                                    <span t-out="line['version_family']" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- FOOTER -->
                <div class="footer o_standard_footer" style="font-size: 12px;">
                    <div class="text-center text-muted">
                        Page: <span class="page" /> / <span class="topage" />
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>
