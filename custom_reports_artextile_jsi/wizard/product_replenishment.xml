<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="form_view_product_replenishment_artextile_jsi" model="ir.ui.view">
        <field name="name">form.view.product.replenishment.artextile.jsi</field>
        <field name="model">product.replenishment.report</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Product Replenishment Report">
                <sheet>
                    <group>
                        <group>
                            <field
                                name="product_ids"
                                widget="many2many_tags"
                                options="{'no_create_edit': True, 'no_open': True}"
                            />
                            <field name="start_date" />
                            <field name="end_date" />
                            <field name="bulk_threshold" />
                            <field name="report_type" />
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button string="Print" name="generate_report" type="object" />
                    <button string="Cancel" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="action_act_window_product_replenishment_artextile_jsi" model="ir.actions.act_window">
        <field name="name">Product Replenishment Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.replenishment.report</field>
        <field name="target">new</field>
        <field name="view_mode">form</field>
    </record>
    <menuitem
        name="Product Replenishment Report"
        action="action_act_window_product_replenishment_artextile_jsi"
        id="menu_action_act_window_product_replenishment_artextile_jsi"
        parent="stock.menu_warehouse_report"
    />
    <record id="action_generate_replenishment_report_artextile_jsi" model="ir.actions.act_window">
        <field name="name">Replenishment Report</field>
        <field name="res_model">product.replenishment.report</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="product.model_product_template" />
        <field name="binding_view_types">list</field>
    </record>
</odoo>
