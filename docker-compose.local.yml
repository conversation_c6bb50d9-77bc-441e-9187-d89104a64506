services:
  db:
    image: postgres:15
    container_name: artx_db
    volumes:
      - artx-db-data:/var/lib/postgresql/data
    env_file: .env
    ports:
      - 5432:5432

  traefik:
    image: traefik:v2.9
    container_name: artx_traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro

  odoo:
    image: odoo:17.0
    container_name: artx_odoo
    env_file: .env
    volumes:
      - artx-odoo-data:/var/lib/odoo
      - ./odoo.local.conf:/etc/odoo/odoo.conf
      - ./custom_addons:/mnt/extra-addons
    labels:
      - traefik.enable=true
      - traefik.http.routers.odoo.rule=Host(`artx.localhost`)
      - traefik.http.routers.odoo.entrypoints=web
      - traefik.http.routers.odoo.middlewares=gzip
      - traefik.http.routers.odoo.service=odoo

      - traefik.http.routers.odoo-im.rule=Host(`artx.localhost`) && Path(`/websocket`)
      - traefik.http.routers.odoo-im.entrypoints=web
      - traefik.http.routers.odoo-im.service=odoo-im
      - traefik.http.routers.odoo-im.middlewares=gzip

      - traefik.http.services.odoo.loadbalancer.server.port=8069
      - traefik.http.services.odoo-im.loadbalancer.server.port=8072
      - traefik.http.middlewares.gzip.compress=true

volumes:
  artx-db-data:
  artx-odoo-data:
