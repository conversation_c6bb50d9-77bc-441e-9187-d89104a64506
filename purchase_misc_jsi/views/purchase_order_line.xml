<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="purchase_order_line_form_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">purchase.order.line.form.purchase.misc.jsi</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <xpath expr="//tree/field[@name='price_subtotal']" position="after">
                <field name="exmill_date" />
                <field name="ready_date" optional="show" />
                <field name="shipping_sch_id" optional="show" options="{'no_create': True, 'no_open': True}" />
                <field name="shipping_status_id" optional="show" options="{'no_open': True}" />
                <field name="shipping_mode_id" optional="show" options="{'no_open': True}" />
                <field name="forwarder_id" optional="hide" options="{'no_open': True}" />
                <field name="etd" optional="show" />
                <field name="eta" optional="show" />
                <field name="vendor_pi_invoice" optional="hide" />
                <field name="vessel_flight" optional="hide" />
                <field name="rolls" optional="hide" />
            </xpath>
        </field>
    </record>
</odoo>
