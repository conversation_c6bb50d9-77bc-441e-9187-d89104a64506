from datetime import date

from odoo import fields, models


class ResCompany(models.Model):
    _inherit = "res.company"

    lock_end_date = fields.Date(string="Lock end date")
    lock_start_date = fields.Date(string="Lock start date")
    lock_end_date_non_adviser = fields.Date(string="Lock end date for non-advisers")
    lock_start_date_non_adviser = fields.Date(string="Lock start date for non-advisers")

    # ref: addons/account/models/company.py (line 238) -> ResCompany._get_user_fiscal_lock_date(self)
    def _get_user_fiscal_lock_start_date(self):
        self.ensure_one()
        if self.user_has_groups("account.group_account_manager"):
            return self.lock_start_date or date.min
        return max(self.lock_start_date or date.min, self.lock_start_date_non_adviser or date.min)

    def _get_user_fiscal_lock_end_date(self):
        self.ensure_one()
        if self.user_has_groups("account.group_account_manager"):
            return self.lock_end_date or date.max
        return min(self.lock_end_date or date.max, self.lock_end_date_non_adviser or date.max)
