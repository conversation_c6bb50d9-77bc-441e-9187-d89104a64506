{
    "name": "Secondary UOM - jsi",
    "summary": """
        1. Allow user to sell product in Yard and manage stock in Meter
        2. Add Total waight in sale order
        3. Sale and Invoice report changes
        4. Set view level Decimal Precision
        """,
    "version": "17.0.1.0.0",
    "author": "Odoo PS",
    "license": "OEEL-1",
    "depends": [
        "sale_management",
        "stock_delivery",
        "delivery",  # For delivery carrier form view
    ],
    "data": [
        "data/uom.xml",
        "views/sale.xml",
        "views/account_move_line.xml",
        "views/stock.xml",
        "report/report_saleorder.xml",
        "report/report_invoice.xml",
    ],
    # Only used to link to the analysis / Ps-tech store
    "task_id": [2519334],
}
