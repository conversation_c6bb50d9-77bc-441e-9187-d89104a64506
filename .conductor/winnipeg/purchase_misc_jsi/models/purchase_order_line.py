from odoo import fields, models


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    shipping_sch_id = fields.Many2one("shipping.schedule", "Shipping Schedule", ondelete="restrict")
    shipping_mode_id = fields.Many2one(related="shipping_sch_id.shipping_mode_id")
    shipping_status_id = fields.Many2one(related="shipping_sch_id.shipping_status_id", string="Shipping Status")
    forwarder_id = fields.Many2one(related="shipping_sch_id.forwarder_id")
    vendor_pi_invoice = fields.Char(related="shipping_sch_id.vendor_pi_invoice", string="Vendor PI")
    vessel_flight = fields.Char(related="shipping_sch_id.vessel_flight")
    rolls = fields.Float(related="shipping_sch_id.rolls")
    etd = fields.Date(related="shipping_sch_id.etd")
    eta = fields.Date(related="shipping_sch_id.eta")
    ready_date = fields.Date("Ready date")
    exmill_date = fields.Date("Exmill date")
