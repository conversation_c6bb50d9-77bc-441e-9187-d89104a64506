from odoo import models


class AccountMove(models.Model):
    _inherit = "account.move"

    def action_register_payment(self):
        """ Override to pass Invoice origin in payment reference field"""
        res = super().action_register_payment()
        res["context"].update(
            {"invoice_origin": ", ".join(self.filtered(lambda x: x.invoice_origin).mapped("invoice_origin"))}
        )
        return res
