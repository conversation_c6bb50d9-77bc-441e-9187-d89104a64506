from odoo import models


class AccountPaymentRegister(models.TransientModel):
    _inherit = "account.payment.register"

    def _create_payment_vals_from_wizard(self, batch_result):
        """
        Override for pass data to payment when we do single payment.
        """
        payment_vals = super()._create_payment_vals_from_wizard(batch_result)
        payment_vals.update({"reference": self.env.context.get("invoice_origin", self.communication)})
        return payment_vals

    def _create_payment_vals_from_batch(self, batch_result):
        """
        Override for pass data to payment when we do batch payment.
        """
        payments_value = super()._create_payment_vals_from_batch(batch_result)
        payments_value.update({"reference": self.env.context.get("invoice_origin", self.communication)})
        return payments_value
