import logging

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from odoo.http import request
from odoo.tools import float_round

# from odoo.addons.website_sale.models.sale_order import SaleOrder as SaleOrderAR
# from odoo.addons.website_sale_stock.models.sale_order import SaleOrder as WebsiteSaleOrderAR
_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = "sale.order"

    cart_quantity = fields.Float(compute="_compute_cart_info")

    @api.depends("order_line.product_uom_qty", "order_line.product_id")
    def _compute_cart_info(self):
        super()._compute_cart_info()
        for order in self:
            order.cart_quantity = round(
                sum(order.website_order_line.mapped("product_uom_qty")), 3
            )

    def _cart_find_product_line(self, product_id=None, line_id=None, **kwargs):
        """
        Override to return blank object to add new line for each quantity
        instead of adding it in existing line
        """
        if not (kwargs.get("add_new_line") or kwargs.get("selected_uom")):
            return super()._cart_find_product_line(
                product_id=product_id, line_id=line_id, **kwargs
            )
        self.ensure_one()
        SaleOrderObj = self.env["sale.order.line"]
        if kwargs.get("add_new_line"):
            return SaleOrderObj
        domain = [
            ("order_id", "=", self.id),
            ("product_id", "=", product_id),
            ("product_uom", "=", int(kwargs.get("selected_uom"))),
        ]
        return SaleOrderObj.sudo().search(domain)

    def _get_deposit_details(self):
        """
        Prepare deposit related data which we render on payment_confirmation_status template.
        """
        self.ensure_one()
        # 1) Initial deposit balance = (partner.credit + confirmed sale with invoice_count=0)
        deposit_data = {}
        orders_with_no_inv = self.read_group(
            [
                ("partner_id", "=", self.partner_id.id),
                ("invoice_ids", "=", False),
                ("state", "in", ["sale", "done"]),
            ],
            ["amount_total"],
            ["partner_id"],
        )
        deposit_data["initial_deposit_balance"] = -(
            self.partner_id.credit
            + (orders_with_no_inv[0]["amount_total"] if orders_with_no_inv else 0.0)
        )
        # 2) Pending orders = (current_order_total + total of all sales with status=sent)
        orders_with_sent_status = self.read_group(
            [("partner_id", "=", self.partner_id.id), ("state", "=", "sent")],
            ["amount_total"],
            ["partner_id"],
        )
        deposit_data["pending_orders_total"] = (
            orders_with_sent_status[0]["amount_total"]
            if orders_with_sent_status
            else 0.0
        )

        # 3) Net deposit balance = 1) + 2)
        deposit_data["net_deposit_balance"] = (
            deposit_data["initial_deposit_balance"]
            - deposit_data["pending_orders_total"]
        )
        return deposit_data

    def _prepare_order_line_values(
        self,
        product_id,
        quantity,
        linked_line_id=False,
        no_variant_attribute_values=None,
        product_custom_attribute_values=None,
        **kwargs
    ):
        """OVERRIDE
        `_cart_update` will convert quantity to Integer
        We need to pass it as given in Input tag on `Product page`
        We also need to change the `product_uom` based on selected in `Product Page`
        """
        res = super(SaleOrder, self)._prepare_order_line_values(
            product_id,
            quantity,
            linked_line_id,
            no_variant_attribute_values,
            product_custom_attribute_values,
            **kwargs
        )
        res.update({"product_uom": int(kwargs.get("selected_uom"))})
        return res

    def _prepare_order_line_update_values(
        self, order_line, quantity, linked_line_id=False, **kwargs
    ):
        """OVERRIDE
        `_cart_update` will convert quantity to Integer
        We need to pass it as given in Input tag on `Product page`
        Update quantity to line as given in `Product Page`
        """
        if kwargs.get("entered_set_qty") not in [False, None]:
            quantity = kwargs.get("entered_set_qty")
        else:
            quantity = float_round(order_line.product_uom_qty, 3) + (
                kwargs.get("entered_add_qty") or 0.0
            )
        return super(SaleOrder, self)._prepare_order_line_update_values(
            order_line=order_line,
            quantity=quantity,
            linked_line_id=linked_line_id,
            **kwargs
        )

    def _cart_update_order_line(self, product_id, quantity, order_line, **kwargs):
        """OVERRIDE
        Let's say we added 0.35 qty in Product page and pressed enter.
        As`_cart_update` will convert quantity to int, so qty will be 0.
        And if 0, this method will remove a line
        Plan is to update qty from context we set in our `_cart_update`
        """
        self.ensure_one()

        if kwargs.get("entered_add_qty") not in [False, None]:
            quantity = kwargs.get("entered_add_qty")
        elif kwargs.get("entered_set_qty") not in [False, None]:
            quantity = kwargs.get("entered_set_qty")
        order_line = super()._cart_update_order_line(
            product_id, quantity, order_line, **kwargs
        )
        return order_line

    def _cart_update(self, product_id, line_id=None, add_qty=0, set_qty=0, **kwargs):
        """OVERRIDE
        This method called when
            - `Add to cart` button
            - `+` and `-` button on `Product page` and `Cart lines`
            - When entered quantity manually on `Product page` and `Cart lines`
            Add or set product quantity, add_qty can be negative
            Extended:
            1)  add new line for each quantity
                add_qty will be None when we add firs time,
                and will be 0 when we proceed for checkout(same method call from the checkout)
            2)  Update line with given UOM
            3)  Allowed to sell quantity as float value
        Pass context for other supporting methods
        """
        if add_qty:
            add_qty = float(add_qty)
        add_new_line = True  # Added
        # Here when you ordered quantity which are not available, odoo make that quantity as -(minus) and call this method again
        # https://github.com/odoo/odoo/blob/14.0/addons/website_sale_stock/models/sale_order.py#L25
        # Now we need to make sure that quantity is - then no need to add new line, update original line with - quantity,
        # and this way it gona remove automatically
        # (It will be None when you delete line in cart)
        if not add_qty or add_qty < 0:
            add_new_line = False

        kwargs.update(
            {
                "entered_add_qty": add_qty,
                "entered_set_qty": set_qty,
                "add_new_line": add_new_line,
            }
        )
        res = super(SaleOrder, self)._cart_update(
            product_id, line_id, add_qty, set_qty, **kwargs
        )
        # Update res with latest quantity
        # this will pass to the `cart_update_json`
        # Why float_round: Add qty=1.234 now click on +. New quantity will be 1.2299999...
        res["quantity"] = float_round(
            self.env["sale.order.line"].browse(res["line_id"]).product_uom_qty, 3
        )

        # Update SO Remark field
        self.write(
            {"x_studio_so_remarks_1": ("貨齊發貨" if self._split_order_check() else "")}
        )

        return res

    def _split_order_check(self, product_id=None):
        """
        Check if order splitting is required
        """
        if product_id:
            product = self.env["product.product"].browse(product_id)
            if product:
                return any(
                    l.product_id.allow_out_of_stock_order
                    != product.allow_out_of_stock_order
                    for l in self.website_order_line
                )

        return any(
            l.product_id.allow_out_of_stock_order == True
            for l in self.website_order_line
        ) and any(
            l.product_id.allow_out_of_stock_order == False
            for l in self.website_order_line
        )
