<?xml version='1.0' encoding='utf-8' ?>
<odoo>
<!--
    Cart Lines
-->
    <template id="cart_lines_website_artextile_jsi" inherit_id="website_sale.cart_lines">
        <!-- Add Remark line -->
        <div t-foreach="website_sale_order.website_order_line" position="replace">
            <div t-foreach="website_sale_order.website_order_line" t-as="line" t-attf-data-product-id="#{line.product_id and line.product_id.id}">
                <t t-if="line.product_id">
                    <div t-attf-class="o_cart_product d-flex align-items-stretch gap-3 #{line.linked_line_id and 'optional_product info'} #{line_index &gt; 0 and 'pt-4'}">
                        <img t-if="line._is_not_sellable_line() and line.product_id.image_128" t-att-src="image_data_uri(line.product_id.image_128)" class="o_image_64_max  img rounded" t-att-alt="line.name_short"/>
                        <div t-else="" t-field="line.product_id.image_128" t-options="{'widget': 'image', 'qweb_img_responsive': False, 'class': 'o_image_64_max rounded'}"/>
                        <div class="flex-grow-1">
                            <t t-call="website_sale.cart_line_product_link">
                                <h6 t-field="line.product_id" class="d-inline align-top h6 fw-bold"/>
                            </t>
                            <t t-call="website_sale.cart_line_description_following_lines">
                                <t t-set="div_class" t-valuef="d-none d-md-block"/>
                            </t>
                            <div>
                                <div t-if="line.product_id.shipping_info" t-attf-class="text-body small #{ 'text-danger' if line.product_id.allow_out_of_stock_order else ''}">Shipping: <span t-field="line.product_id.shipping_info"/></div>
                                <a href="#" class="js_delete_product d-none d-md-inline-block small" aria-label="Remove from cart" title="Remove from cart">Remove</a>
                                <button class="js_delete_product btn btn-light d-inline-block d-md-none" title="remove">
                                    <i class="fa fa-trash-o"/>
                                </button>
                            </div>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <div t-attf-class="css_quantity input-group mb-2" name="website_sale_cart_line_quantity">
                                <t t-if="not line._is_not_sellable_line()">
                                    <t t-if="show_qty">
                                        <a href="#" class="js_add_cart_json btn btn-link d-inline-block border-end-0" aria-label="Remove one" title="Remove one">
                                            <i class="position-relative z-index-1 fa fa-minus"/>
                                        </a>
                                        <input type="text" t-att-data-max-allowed-qty="line.company_id.allowed_max_sale_qty" class="js_quantity quantity form-control border-start-0 border-end-0" t-att-data-line-id="line.id" t-att-data-product-id="line.product_id.id" t-att-value="line._get_displayed_quantity()"/>
                                        <t t-if="line._get_shop_warning(clear=False)">
                                            <a href="#" class="btn btn-link">
                                            <i class="fa fa-warning text-warning" t-att-title="line._get_shop_warning()" role="img" aria-label="Warning"/>
                                            </a>
                                        </t>
                                        <a t-else="" href="#" class="js_add_cart_json d-inline-block float_left btn btn-link border-start-0" aria-label="Add one" title="Add one">
                                            <i class="fa fa-plus position-relative z-index-1"/>
                                        </a>
                                    </t>
                                    <t t-else="">
                                        <input type="hidden" class="js_quantity form-control quantity" t-att-data-line-id="line.id" t-att-data-product-id="line.product_id.id" t-att-value="line._get_displayed_quantity()"/>
                                    </t>
                                </t>
                                <t t-else="">
                                    <span class="w-100 text-muted" t-out="line._get_displayed_quantity()"/>
                                    <input type="hidden" class="js_quantity quantity form-control" t-att-data-line-id="line.id" t-att-data-product-id="line.product_id.id" t-att-value="line.product_uom_qty"/>
                                </t>
                                <a href="#" class="disabled btn btn-link d-inline-block" aria-label="UOM" title="UOM">
                                    <i class="position-relative z-index-1"><t t-out="line.product_uom.name"/></i>
                                </a>
                            </div>
                            <div class="mb-0 h6 fw-bold text-end" name="website_sale_cart_line_price">
                                <t t-if="line.discount">
                                    <del t-attf-class="#{'text-danger mr8'}" style="white-space: nowrap;" t-out="line._get_displayed_unit_price()" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                </t>
                                <t t-if="website.show_line_subtotals_tax_selection == 'tax_excluded'" t-set="product_price" t-value="line.price_subtotal"/>
                                <t t-else="" t-set="product_price" t-value="line.price_total"/>
                                <t t-set="base_unit_price" t-value="line.product_id._get_base_unit_price(product_price/line.product_uom_qty)" />
                                <span t-out="base_unit_price" style="white-space: nowrap;" t-options="{'widget': 'monetary', 'display_currency': website_sale_order.currency_id}" />
                                <small t-if="not line._is_not_sellable_line() and line.product_id.base_unit_price" class="cart_product_base_unit_price d-block text-muted" groups="website_sale.group_show_uom_price">
                                    <t t-call="website_sale.base_unit_price">
                                        <t t-set="product" t-value="line.product_id"/>
                                        <t t-set="combination_info" t-value="{'base_unit_price': base_unit_price}"/>
                                    </t>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div style="border-bottom: 2px solid black;" class="mt-2">
                        <style>::placeholder {color: #d2d2d2 !important;}</style>
                        <div class="input-group mx-auto" style="padding-bottom: 5px;">
                            <div style="padding: 0.375rem 0.75rem; width: 130px;">Order#</div>
                            <input type="text" class="form-control line_customer_po"
                                t-att-data-line-id="line.id"
                                t-att-data-product-id="line.product_id.id"
                                t-att-value="line.customer_po or ''"
                                placeholder="eg: customer order#"
                                style="color: #003163 !important;"
                            />
                        </div>
                        <div class="input-group mx-auto" style="padding-bottom: 5px;">
                            <div style="padding: 0.375rem 0.75rem; width: 130px;">Other Remarks:</div>
                            <input type="text" class="form-control line_remark"
                                t-att-data-line-id="line.id"
                                t-att-data-product-id="line.product_id.id"
                                t-att-value="line.remarks or ''"
                                placeholder="eg: match color of previous order?/label requirements/other remarks"
                                style="color: #003163 !important;"
                            />
                        </div>
                    </div>
                </t>
            </div>
        </div>
    </template>

    <!-- Add status availability message on Product Listing and Product page and should visible for public user-->
    <template id="product_stock_status_ar" name="Product Stock Status">
        <t t-set="product" t-value="product.sudo()"/>
        <t t-set="free_qty" t-value="product.qty_total"/>
        <t t-set="stock_threshold" t-value="product.available_threshold"/>
        <t t-set="stock_qty_threshold" t-value="website.company_id.stock_qty_threshold"/>
        <div t-if="product.allow_out_of_stock_order" t-att-class="class_product_page_ar">
            <span>Available</span>
            <div t-if="product.shipping_info" class="text-body small">Shipping: <span t-field="product.shipping_info"/></div>
        </div>
        <div t-else="" t-att-class="class_product_page_ar">
            <t t-if="free_qty &gt; stock_threshold">
                <div class="text-success mt-0">
                    <span>Available</span>
                    <div t-if="product.shipping_info" class="text-body small">Shipping: <span t-field="product.shipping_info"/></div>
                </div>
            </t>
            <t t-elif="stock_threshold &gt; free_qty and free_qty &gt; stock_qty_threshold">
                <div class="text-warning-light-ar">
                    <i class="fa fa-exclamation-triangle" title="Warning" role="img" aria-label="Warning"/>
                    <span>Low Stock<t t-if="low_stock_msg"> - <span t-out="low_stock_msg"/></t></span>
                </div>
                <t t-if="product.stock_availability"><span t-field="product.stock_availability"/>.</t>
                <t t-if="product.temp_out_until">
                    <span>Available from <em><span t-out="product.temp_out_until"/>.</em></span>
                </t>
                <div class="small" t-if="product.stock_availability == 'low_stock'" t-out="low_stock_msg"/>
            </t>
            <t t-elif="stock_qty_threshold &gt; free_qty">
                <div class="text-warning-ar">
                    <i class="fa fa-exclamation-triangle" title="Sold Out" role="img" aria-label="Sold Out"/>
                    <span>Sold Out</span>
                </div>
                <div>
                    <t t-if="product.stock_availability"><span t-field="product.stock_availability"/>.</t>
                    <t t-if="product.temp_out_until">
                        <span>Available from <em><span t-out="product.temp_out_until"/>.</em></span>
                    </t>
                </div>
            </t>
        </div>
    </template>

<!--
    Product Listing Page
-->
    <template id="products_item_website_artextile_jsi" inherit_id="website_sale.products_item" priority="90">

        <!-- all data centered -->
        <form position="attributes">
            <attribute name="class" separator=" " add="text-center"/>
        </form>

        <!-- Add default code -->
        <a t-field="product.name" position="attributes">
            <attribute name="t-field">product.display_name</attribute>
        </a>

        <!-- Remove padding after price -->
        <xpath expr="//div[hasclass('o_wsale_product_sub')]" position="attributes">
            <attribute name="class" separator=" " remove="gap-2 pb-1" />
        </xpath>

        <!--
            1) We need to display higher Price on Product Listing page
               So, if discount is positive on pricelist, display amount without discount(as it will be higher than the discounted amount)
               otherwise display amount with discount
            2) Hide price from public user
        -->
        <div class="product_price" position="replace">
            <div class="product_price text-center col-12 mb-0" itemprop="offers" itemscope="itemscope" itemtype="http://schema.org/Offer" t-if="not request.website.is_public_user()">
                <t t-if="'base_price' in template_price_vals and (template_price_vals['base_price'] &gt; template_price_vals['price_reduce'])">
                    <span class="mb-0" t-esc="template_price_vals['base_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
                </t>
                <t t-else="">
                    <span class="mb-0" t-if="template_price_vals['price_reduce']" t-esc="template_price_vals['price_reduce']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                </t>
                <span itemprop="price" style="display:none;" t-esc="template_price_vals['price_reduce']" />
                <span itemprop="priceCurrency" style="display:none;" t-esc="website.currency_id.name" />
            </div>
            <t t-call="website_artextile_jsi.product_stock_status_ar">
                <t t-set="class_product_page_ar" t-value="'text-center col-12 mt-0'"/>
            </t>
        </div>
        <div class="o_wsale_product_btn" position="replace" />
    </template>

    <!-- CATEGORY FILTER -->
    <template id="categories_recursive_website_artextile_jsi" inherit_id="website_sale.categories_recursive">
        <!-- Load all category before and after search-->
        <ul t-if="c.child_id" position="replace">
            <ul t-if="c.child_id" class="nav flex-column nav-hierarchy mt-1 ps-3">
                <t t-foreach="c.child_id" t-as="c">
                    <t t-call="website_sale.categories_recursive"/>
                </t>
            </ul>
        </ul>
    </template>

    <template id="categorie_link_website_artextile_jsi" inherit_id="website_sale.categorie_link">
        <!-- Keep plain category href without `search` -->
        <xpath expr="//div[hasclass('d-inline-block')]" position="attributes">
            <attribute name="t-att-data-link-href">'/shop/category/' + slug(c)</attribute>
        </xpath>
    </template>

    <template id="products_categories_list_website_artextile_jsi" inherit_id="website_sale.products_categories_list">
        <!-- Keep plain href: shop on All Products-->
        <div class="form-check d-inline-block" position="attributes">
            <attribute name="t-att-data-link-href">'/shop'</attribute>
        </div>
    </template>

<!--
    Product Page
-->
    <template id="product_website_artextile_jsi" inherit_id="website_sale.product">
        <t t-set="additional_title" position="attributes">
            <attribute name="t-value">product.display_name</attribute>
        </t>
        <xpath expr="//li[@class='breadcrumb-item active']/span" position="attributes">
            <attribute name="t-field">product.display_name</attribute>
        </xpath>
        <h1 itemprop="name" position="attributes">
            <attribute name="t-field">product.display_name</attribute>
        </h1>

        <!-- price only for login user -->
        <xpath expr="//div[hasclass('js_main_product')]/div" position="attributes">
            <attribute name="t-if">not request.website.is_public_user()</attribute>
        </xpath>

        <!-- Add status availability message -->
        <xpath expr="//div[hasclass('availability_messages')]" position="replace">
            <t t-call="website_artextile_jsi.product_stock_status_ar">
                <t t-set="class_product_page_ar" t-value="'col-12 mt-0'"/>
                <t t-set="low_stock_msg" t-value="'Please contact us before ordering'"/>
            </t>
        </xpath>

        <!-- Add brand_full_name after product name -->
        <h1 itemprop="name" position="after">
            <div style="font-size: 1.3rem;">
                <span t-field="product.sudo().brand_id.brand_full_name" class="text-muted mt-3"/>
            </div>
        </h1>
        <!-- Hide add to cart in product form -->
        <a id="add_to_cart" position="attributes">
            <attribute name="t-if">not request.website.is_public_user()</attribute>
        </a>
        <!-- Custom fields -->
        <p t-field="product.description_sale" position="replace"/>
        <div t-field="product.description_ecommerce" position="replace"/>
        <div id="product_documents" position="after">
            <hr />
            <h5><u>PRODUCT DETAILS</u></h5>
            <div t-if="product.description">
                <strong>Composition:</strong>
                <span t-field="product.description" class="text-muted" t-options="{'widget': 'text'}"/>
            </div>
            <div t-if="product.sudo().product_yarn_id and 'N/A' not in product.sudo().product_yarn_id.name">
                <strong>Yarn Count/Fineness:</strong>
                <span t-field="product.sudo().product_yarn_id" class="text-muted" />
            </div>
            <div t-if="product.sudo().average_weight_g">
                <strong>Weight (gram/meter):</strong>
                <span t-out="int(product.sudo().average_weight_g)" class="text-muted" />
            </div>
            <div t-if="product.width_cm">
                <strong>Width (cm):</strong>
                <span t-out="int(product.width_cm)" class="text-muted" />
            </div>
            <div t-if="product.product_feature">
                <strong>Feature:</strong>
                <span t-field="product.product_feature" class="text-muted" />
            </div>
            <div t-if="product.collection_name_full">
                <strong>Collection:</strong>
                <span t-field="product.collection_name_full" class="text-muted" />
            </div>
            <div t-if="product.sample_bookcat">
                <strong>Sample Book:</strong>
                <span t-field="product.sample_bookcat" class="text-muted" />
            </div>
            <p t-field="product.ecom_remarks" class="font-weight-bold my-2" placeholder="A short description that will also appear on documents."/>
        </div>
    </template>

    <!-- Remove extra text from Term and condition -->
    <template id="product_custom_text_website_artextile_jsi" inherit_id="website_sale.product_custom_text">
        <xpath expr="//a[@href='/terms']/.." position="replace">
            <p class="text-muted mb-0">
                <a href="/terms" class="text-muted"><u>Terms and Conditions</u></a><br/>
            </p>
        </xpath>
    </template>

    <!-- Hide whishlist options on Product Page -->
    <record id="website_sale_wishlist.product_add_to_wishlist" model="ir.ui.view">
        <field name="active">False</field>
    </record>

    <!-- Price page inside Product Page-->
    <template id="product_price_website_artextile_jsi" inherit_id="website_sale.product_price">
        <!-- Move original price before discounted price -->
        <span class="oe_price" position="before">
            <span t-esc="combination_info['list_price']" position="move" />
        </span>

        <!-- Disply USD price on product detail page -->
        <xpath expr="//h3[hasclass('css_editable_mode_hidden')]" position="inside">
            <div class="font-16px-ar text-muted font-italic">
                (=<span t-attf-class="text-danger oe_default_price_usd {{'' if combination_info['has_discounted_price'] else 'd-none'}}" style="text-decoration: line-through; white-space: nowrap;" t-esc="combination_info['list_price_usd']" t-options="{'widget': 'monetary', 'display_currency': env.ref('base.USD')}"/>
                <span class="oe_price_usd" style="white-space: nowrap;" t-esc="combination_info['price_usd']" t-options="{'widget': 'monetary', 'display_currency': env.ref('base.USD')}"/>, reference only)
            </div>
        </xpath>

    </template>

    <!-- UOM Selection inside Product Page-->
    <template id="product_quantity_website_artextile_jsi" inherit_id="website_sale.product_quantity">
        <xpath expr="//div[contains(@t-attf-class, 'css_quantity')]" position="inside">
            <t t-set="uoms" t-value="product.sudo().get_uom()"/>
            <t t-if="uoms">
                <select id="uom_selector" name="uom_id" class="btn btn-secondary">
                    <t t-foreach="uoms['uom_list']" t-as="uom">
                        <option t-att-value="uom['id']" t-att-selected="uom['id'] ==  uoms['default']">
                            <t t-out="uom['name']"/>
                        </option>
                    </t>
                </select>
            </t>
        </xpath>
        <!-- Hide Quantity input  -->
        <xpath expr="//div[contains(@t-attf-class, 'css_quantity')]" position="attributes">
            <attribute name="t-if">not request.website.is_public_user()</attribute>
        </xpath>
        <!-- Add max allowed qty -->
        <input name="add_qty" position="attributes">
            <attribute name="t-att-data-max-allowed-qty">request.website.company_id.allowed_max_sale_qty</attribute>
            <attribute name="style">max-width: 105px;</attribute>
            <attribute name="t-att-value">add_qty == 1 and '1.000' or add_qty</attribute>
        </input>
    </template>

<!--
    Extra Step page
-->
    <!-- <template id="extra_info_website_artextile_jsi" inherit_id="website_sale.extra_info"> -->
        <!-- Remove Your reference -->
        <!-- <xpath expr="//div[hasclass('s_website_form_rows')]/div[2]" position="replace" /> -->
        <!-- Remove Feedback -->
        <!-- <xpath expr="//div[hasclass('s_website_form_rows')]/div[1]" position="replace" /> -->
        <!-- Add Bill to -->
        <!-- <xpath expr="//div[hasclass('s_website_form_field')]" position="before">
            <div class="s_website_form_field col-12 py-2 mb-0" data-type="char" data-name="Field">
                <div class="s_col_no_resize s_col_no_bgcolor row">
                    <label class="s_website_form_label col-form-label col-sm-auto" style="width: 200px" for="full_name">
                        <span class="s_website_form_label_content">Bill To</span>
                    </label>
                    <div class="col-sm">
                        <input id="full_name" type="text" class="s_website_form_input form-control" name="full_name" t-att-value="website_sale_order.full_name"/>
                    </div>
                </div>
            </div>
        </xpath> -->
        <!-- Change label for Document -->
        <!-- <xpath expr="//input[@name='a_document']/../../label/span" position="replace"> -->
            <!-- <span class="s_website_form_label_content">Attachment (eg. packing list)</span> -->
        <!-- </xpath> -->
    <!-- </template> -->

<!--
    Checkout page
-->
    <template id="checkout_website_artextile_jsi" inherit_id="website_sale.checkout">
        <!-- Remove Billing Address from Checkout page -->
        <t t-call="website_sale.row_addresses" position="replace" />
        <xpath expr="//div[hasclass('row')]" position="replace"/>
    </template>

    <!-- Address kanban in Checkout page for shipping address selection -->
    <template id="address_kanban_website_artextile_jsi" inherit_id="website_sale.address_kanban">
        <!-- Need to remove edit button and change the address format -->
        <t t-esc="contact" position="replace">
            <span>Address Label:</span> <span t-esc="contact.name"/> <br/>
            <t t-call="website_artextile_jsi.common_address_format_website_artextile_jsi"><t t-set="address_partner" t-value="contact"/></t>
        </t>
        <!-- Remove classes to look like v14 -->
        <xpath expr="//div[hasclass('card-body')]" position="attributes">
            <attribute name="class" separator=" " remove="d-flex flex-column"/>
        </xpath>
    </template>

    <!-- Address edit page in Checkout step when we click on address kanban / Shipping tab-->
    <template id="address_website_artextile_jsi" inherit_id="website_sale.address">
        <!-- New fields -->
        <xpath expr="//label[@for='name']/.." position="after">
            <div t-attf-class="#{error.get('contact') and 'o_has_error'} div_contact col-lg-12 mb-2">
                <label class="col-form-label" for="contact">Contact</label>
                <input type="text" name="contact" t-attf-class="form-control #{error.get('contact') and 'is-invalid' or ''}" t-att-value="'contact' in checkout and checkout['contact']"/>
            </div>
            <div t-attf-class="#{error.get('full_name') and 'o_has_error'} div_full_name col-lg-12 mb-2">
                <label class="col-form-label" for="full_name">Company Name</label>
                <input type="text" name="full_name" t-attf-class="form-control #{error.get('full_name') and 'is-invalid' or ''}" t-att-value="'full_name' in checkout and checkout['full_name']"/>
            </div>
        </xpath>
        <!-- change label Name to Address Label -->
        <label for="name" position="replace">
            <label class="col-form-label" for="name">Address Label</label>
        </label>
        <!-- Make <input> readonly for name and pass `New` as a default value as it's required field -->
        <input name="name" position="attributes">
            <attribute name="readonly">readonly</attribute>
            <attribute name="t-att-value">'name' in checkout and checkout['name'] or 'New'</attribute>
        </input>
        <!-- Remove Email -->
        <div id="div_email" position="replace" />
    </template>

    <!-- Checkout Summary panel -->
    <template id="checkout_layout_website_artextile_jsi" inherit_id="website_sale.checkout_layout">
        <!-- Remove pt-o from all td -->
        <t t-set="o_cart_sum_padding_top" position="replace" />

        <!-- Bigger summary panel -->
        <xpath expr="//div[@id='o_wsale_total_accordion']" position="attributes">
            <attribute name="class" separator=" " remove="offset-xl-1 col-xl-4" add="col-xs-5" />
        </xpath>

        <!-- Remove Items info -->
        <xpath expr="//button[hasclass('accordion-button')]/div" position="replace">
            <div class="d-flex flex-wrap">
                <b class="w-100">Order summary</b>
            </div>
        </xpath>

        <!-- Add column header -->
        <xpath expr="//table[@id='cart_products']/tbody" position="before">
            <thead>
                <th colspan="2"><span>Product</span></th>
                <th class="text-center"><span>Quantity</span></th>
                <th class="text-end"><span>Price</span></th>
                <th class="text-end"><span>Subtotal</span></th>
            </thead>
        </xpath>

        <!-- Update summary data -->
        <xpath expr="//td[@name='website_sale_cart_summary_product_name']/h6" position="replace">
            <strong t-field="line.product_id"/>
        </xpath>
        <xpath expr="//td[@name='website_sale_cart_summary_product_name']" position="after">
            <td class="text-center">
                <span t-out="'%.3f' % line.product_uom_qty"/>
                <span t-field="line.product_uom"/>
                <t t-if="line._get_shop_warning(clear=False)">
                    <i class="fa fa-warning text-warning" role="img" t-att-title="line._get_shop_warning()" aria-label="Warning"/>
                </t>
            </td>
            <td class="text-end">
                <span t-field="line.price_reduce_taxexcl" />
            </td>
        </xpath>

    </template>


<!--
    Payment page
-->

    <template id="address_on_payment_website_artextile_jsi" inherit_id="website_sale.address_on_payment">
        <!-- Remove Billing Address, add Contact from Shipping Address and Change Edit to Change -->
        <div id="shipping_and_billing" position="replace">
            <div class="card-body" id="shipping_and_billing">
                <!-- <a class="float-right no-decoration" href="/shop/checkout"><i class="fa fa-edit"/> </a> -->
                <a t-if="not disable_edit" class="float-end no-decoration" href="/shop/checkout"><i class="fa fa-edit me-1"/>Change</a>
                <div><b>Shipping: </b><span t-out="order.partner_shipping_id.contact"/>, <span t-out="order.partner_shipping_id" t-options="dict(widget='contact', fields=['address'], no_marker=True, separator=', ')" class="address-inline"/></div>
            </div>
        </div>
    </template>

    <template id="payment_confirmation_status_website_artextile_jsi" inherit_id="website_sale.payment_confirmation_status">
        <!-- Display deposit data on payment confirmation page-->
        <xpath expr="//div[hasclass('card-header')]" position="inside">
            <!-- Need to display this section only if Display Balance is checked in Payment Acquirer -->
            <t t-if="tx_sudo.provider_id.display_balance">
                <!--
                    There is one warning sign coming if payment is pending, client want to remove it
                    https://github.com/odoo/odoo/blob/14.0/addons/website_sale/static/src/js/website_sale_validate.js#L43
                    It add this sign after the "Initial Deposit Balance:", which is little confusing.
                -->
                <div class="text-disply-none-ar"><span  /></div>

                <!-- Display deposit data-->
                <t t-set="deposit_data" t-value="order._get_deposit_details()" />
                <t t-if="deposit_data['net_deposit_balance'] >= 0">
                    <t t-raw="tx_sudo.provider_id.message_sufficient"/>
                </t>
                <t t-else="">
                    <t t-raw="tx_sudo.provider_id.message_insufficient"/>
                </t>
                <strong>
                    Initial Deposit Balance:
                    <t t-out="deposit_data['initial_deposit_balance']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
                </strong>
                <br/>
                <strong>
                    Pending Orders:
                    <t t-out="deposit_data['pending_orders_total']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                    <a class="text-hyperlink-ar" href="/my/quotes">
                        (<u>details</u>)
                    </a>
                </strong>
                <br/>
                <strong>
                    Net Deposit Balance:
                    <t t-out="deposit_data['net_deposit_balance']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
                </strong>
            </t>
        </xpath>
    </template>

<!--
    PORTAL
-->

    <template id="common_address_format_website_artextile_jsi">
        <!-- Need to remove ', ' if no contact or only phone -->
        <div><strong t-out="address_partner.contact and address_partner.phone and (address_partner.contact + ', ') or address_partner.contact"/><span t-out="address_partner.phone"/></div>
        <div t-out="address_partner.street"/>
        <div t-out="address_partner.street2"/>
        <span t-out="address_partner.city"/> <span t-out="address_partner.state_id.name"/> <span t-out="address_partner.zip"/>
        <div t-out="address_partner.country_id.name"/>
    </template>

    <!-- Change address format on `My Account` page -->
    <template id="side_content_website_artextile_jsi" inherit_id="portal.side_content">
        <!-- No more Edit button on Portal page-->
        <a href="/my/account" position="replace"/>
        <!-- Reformat the Address -->
        <div class="o_portal_my_details" position="replace">
            <div class="o_portal_my_details">
                <div t-field="user_id.partner_id"/>
                <span>Company Name: </span> <span t-field="user_id.partner_id.full_name"/> <br/>
                <t t-call="website_artextile_jsi.common_address_format_website_artextile_jsi"><t t-set="address_partner" t-value="user_id.partner_id"/></t>
                <strong>Email: </strong><span t-field="user_id.partner_id.email"/>
            </div>
        </div>
    </template>

    <template id="portal_my_home_website_artextile_jsi" inherit_id="portal.portal_my_home">
        <div class="o_portal_my_home" position="before">
            <t t-set="perf_chart" t-value="request.env.user.partner_id.perf_chart_ids.filtered(lambda p: p.is_display)"/>
            <div t-if="perf_chart" class="mb-4">
                <h3>Performance</h3>
                <section class="s_numbers o_cc o_cc2 pt0 pb0" t-if="len(perf_chart)">
                    <div class="container">
                    <div class="row">
                        <div class="col-lg-4 pb24 pt24 text-center">
                            <span class="display-4 s_number font-30px-ar">
                                <span t-out="'{:,.0f}'.format(perf_chart[0].target)"/>
                            </span>
                            <h6>Target (HKD)</h6>
                        </div>
                        <div class="col-lg-4 pb24 pt24 text-center text-hyperlink-ar">
                            <span class="display-4 s_number font-30px-ar">
                                <span t-out="'{:,.0f}'.format(perf_chart[0].actual_display)"/>
                            </span>
                            <h6 class="text-hyperlink-ar">Actual (HKD)</h6>
                        </div>
                        <div class="col-lg-4 pb24 pt24 text-center text-hyperlink-ar">
                            <span class="display-4 s_number font-30px-ar">
                                <t t-out="round(perf_chart[0].attainment_rate, 2)"/> %
                            </span>
                            <h6 class="text-hyperlink-ar">Attainment %</h6>
                        </div>
                    </div>
                    </div>
                </section>
                <section t-if="perf_chart" class="s_text_block o_colored_level pt0 undefined o_cc o_cc1 pb24" data-snippet="s_text_block" data-name="Text">
                    <div class="col-12 pl24 pr24">
                        <strong>Remarks</strong>
                        <p>
                            The period of the target is: <t t-out="perf_chart[0].date_from" t-options="{'widget': 'date', 'format': 'YYYY, MMM d'}"/> 日 to <t t-out="perf_chart[0].date_to" t-options="{'widget': 'date', 'format': 'YYYY, MMM d'}"/> 日 day. <br/>
                            <t t-raw="perf_chart[0].message_display"/>
                        </p>
                    </div>
                </section>
            </div>
        </div>
    </template>

    <template id="show_delivery_lead_time_reminder" inherit_id="payment.submit_button">
        <xpath expr="//button[@name='o_payment_submit_button']" position="after">
            <t t-if="website_sale_order and website_sale_order._split_order_check()">
                <div class="text-danger py-2">
                    Notice: Your order includes items with different delivery lead times. The order will be delivered when all items have arrived.
                </div>
            </t>
        </xpath>
    </template>

</odoo>
