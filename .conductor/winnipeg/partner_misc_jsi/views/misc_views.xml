<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="view_form_company_group_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.company.group.partner.misc.jsi</field>
        <field name="model">company.group</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="name" />
                        <field name="description" />
                    </group>
                    <group />
                </group>
            </form>
        </field>
    </record>
    <record id="view_tree_company_group_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.company.group.partner.misc.jsi</field>
        <field name="model">company.group</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="description" />
            </tree>
        </field>
    </record>
    <record id="action_company_group_partner_misc_jsi" model="ir.actions.act_window">
        <field name="name">Company Group</field>
        <field name="res_model">company.group</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_company_group_partner_misc_jsi"
        name="Company Group"
        action="action_company_group_partner_misc_jsi"
        parent="contacts.res_partner_menu_config"
        sequence="3"
    />
    <record id="view_form_sub_sales_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.sub.sales.partner.misc.jsi</field>
        <field name="model">sub.sales</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="name" />
                        <field name="user_id" />
                    </group>
                    <group />
                </group>
            </form>
        </field>
    </record>
    <record id="view_tree_sub_sales_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.sub.sales.partner.misc.jsi</field>
        <field name="model">sub.sales</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="user_id" />
            </tree>
        </field>
    </record>
    <record id="action_sub_sales_partner_misc_jsi" model="ir.actions.act_window">
        <field name="name">Sub Sales</field>
        <field name="res_model">sub.sales</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_sub_sales_partner_misc_jsi"
        name="Sub Sales"
        action="action_sub_sales_partner_misc_jsi"
        parent="contacts.res_partner_menu_config"
        sequence="4"
    />
    <record id="view_form_partner_market_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.partner.market.partner.misc.jsi</field>
        <field name="model">partner.market</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="name" />
                        <field name="parent_id" />
                        <field name="parent2_id" />
                    </group>
                    <group />
                </group>
            </form>
        </field>
    </record>
    <record id="view_tree_partner_market_partner_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.partner.market.partner.misc.jsi</field>
        <field name="model">partner.market</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="parent_id" />
                <field name="parent2_id" />
            </tree>
        </field>
    </record>
    <record id="action_partner_market_partner_misc_jsi" model="ir.actions.act_window">
        <field name="name">Market</field>
        <field name="res_model">partner.market</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_partner_market_partner_misc_jsi"
        name="Market"
        action="action_partner_market_partner_misc_jsi"
        parent="contacts.res_partner_menu_config"
        sequence="5"
    />
</odoo>
