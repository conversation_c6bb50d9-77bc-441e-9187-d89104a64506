from odoo import api, fields, models


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    is_label_printed = fields.Boolean("Label Printed")
    no_of_label_printed = fields.Integer("No. of Label Printed")
    remarks = fields.Char("Remarks")
    customer_po = fields.Char("Customer PO Number", size=30)

    @api.model_create_multi
    def create(self, vals_list):
        """
        If product type service, no need to print label by default
        Client going to add shipping product from Delivery carrier wizard, no way to make it true on the fly.
        """
        Product = self.env["product.product"]
        for vals in vals_list:
            if vals.get("product_id"):
                product = Product.browse(vals["product_id"])
                if product.type == "service":
                    vals["is_label_printed"] = True
        return super().create(vals_list)
