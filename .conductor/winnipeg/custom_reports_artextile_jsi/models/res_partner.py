import base64
import io

from odoo import _, fields, models
from odoo.tools.misc import xlsxwriter
from odoo.tools import float_round
from odoo.tools.misc import formatLang, format_date, get_lang
import datetime


class ResPartner(models.Model):
    _inherit = "res.partner"

    exclude_replenishment_report = fields.Boolean("Exclude Replenishment Report")

    def print_followup_report(self, report_lang_cn=False):
        # printing followup report in certaing language
        lang = "en_US"
        report_name = "PartnerFollowup.xlsx"
        if report_lang_cn:
            lang = 'zh_CN'
            report_name = "PartnerFollowup_CN.xlsx"
        self = self.with_context(lang=lang)
        options = {
            'partner_id': self.id,
            'followup_line': self.followup_line_id,
            'context': self.env.context,
        }
        # Prevent inconsistency between options and context.
        AccountFollowupReportObj = self.env["account.followup.report"]

        # Create lines/headers.
        lines = AccountFollowupReportObj.with_context(print_mode=True)._get_followup_report_lines(options)
        report_data = {
            "customer": self.full_name,
            "code": self.code,
            "report_date": fields.date.today().strftime("%d/%m/%Y"),
            "lines": lines,
        }
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {"in_memory": True})
        bold = workbook.add_format({"bold": True})
        float_2dp = workbook.add_format({'num_format': '#,##0.00'})

        worksheet = workbook.add_worksheet()
        worksheet.write(0, 0, _("FOLLOWUP REPORT"), bold)
        worksheet.write(1, 0, _("Customer"), bold)
        worksheet.write(1, 1, report_data["customer"])
        worksheet.write(2, 0, _("Customer Code"), bold)
        worksheet.write(2, 1, report_data["code"])
        worksheet.write(3, 0, _("Report Date"), bold)
        worksheet.write(3, 1, report_data["report_date"])

        table_header = [_("Journal No."), _("Date"), _("Due Date"), _("Source Document"), _("Communication"), _("Amount"), _("Due amount")]
        row = 5
        col = 0
        for head in table_header:
            worksheet.write(row, col, head, bold)
            col += 1
        row = 6
        for line in report_data["lines"]:
            worksheet.write(row, 0, line.get("name"))
            col = 1
            for column in line["columns"]:
                if col in (1, 2):  # related to dates
                    date = column['name']
                    reformat_date = datetime.datetime.strptime(date, '%m/%d/%Y').strftime('%d/%m/%Y') if date else date
                    worksheet.write(row, col, reformat_date)
                elif col == 4:  # Communication
                    communication_ref = '%s-%s' % (line.get("name"), column['name'])
                    worksheet.write(row, col, communication_ref)
                elif col == 5:
                    total = (
                        line.get("account_move")
                        and formatLang(self.env, line["account_move"].amount_total, currency_obj=self.currency_id)
                        or ""
                    )
                    total = total.replace(self.currency_id.symbol, '').replace(',', '').strip()
                    column['name'] = column['name'].replace(',', '').replace(self.currency_id.symbol, '').strip()
                    if total:
                        worksheet.write_number(row, col, float_round(float(total), precision_digits=2), float_2dp)
                    if column['name']:
                        worksheet.write_number(row, col + 1, float_round(float(column["name"]), precision_digits=2), float_2dp)
                else:
                    worksheet.write(row, col, column["name"])
                col += 1
            row += 1

        workbook.close()
        xlsx_data = output.getvalue()
        file_data = base64.encodebytes(xlsx_data)
        followup_report = self.env['res.partner.followup.report'].sudo().create({"file_data": file_data})
        return {
            "type": "ir.actions.act_url",
            "url": "/web/content/?model={}&id={}&field=file_data&filename={}&download=true".format(
                followup_report._name, followup_report.id, report_name
            ),
            "target": "self",
        }
