from odoo import api, models


class MrpBom(models.Model):
    _inherit = "mrp.bom"

    @api.model_create_multi
    def create(self, vals_list):
        """
        override to set fields values on products
        """
        for values in vals_list:
            product = self.env["product.template"].browse(values.get("product_tmpl_id"))
            if not product.bom_ids:
                """Only for a product without bom / setting values only when first bom link"""
                product.write(
                    {
                        "route_ids": [(6, 0, self.env.ref("mrp.route_warehouse0_manufacture").ids)],
                        "tracking": "none",
                        "purchase_ok": False,
                        "is_master": False,
                    }
                )
        return super().create(vals_list)

    def button_add_new_master_product(self):
        """
        Add component as master_product_id of BOM product.
        There will be always one component.
        """
        for bom in self:
            if bom.bom_line_ids:
                bom.product_tmpl_id.master_product_id = bom.bom_line_ids[0].product_id.product_tmpl_id
                bom.product_tmpl_id.onchange_master_product_id()
                bom.bom_line_ids[0].product_id.product_tmpl_id.write(
                    {
                        "master_product_id": False,
                        "is_master": True,
                    }
                )
        return True
